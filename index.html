<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Math Adventure Game</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Fredoka+One&family=Roboto&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Roboto', sans-serif;
        }
        h1, h2, h3, .fredoka {
            font-family: 'Fredoka One', cursive;
        }
        .screen {
            display: none;
        }
        .screen.active {
            display: block;
        }
        .checkbox-label {
            transition: all 0.2s ease-in-out;
        }
        .checkbox-label:hover {
            transform: scale(1.05);
        }
        .correct-animation {
            animation: correct 0.5s ease-in-out;
        }
        @keyframes correct {
            0% { transform: scale(1); }
            50% { transform: scale(1.1) rotate(3deg); background-color: #dcfce7; }
            100% { transform: scale(1); }
        }
        .incorrect-animation {
            animation: incorrect 0.5s ease-in-out;
        }
        @keyframes incorrect {
            0%, 100% { transform: translateX(0); }
            20% { transform: translateX(-10px); }
            40% { transform: translateX(10px); }
            60% { transform: translateX(-10px); }
            80% { transform: translateX(10px); }
        }
        /* Hide spin buttons on number inputs */
        input[type=number]::-webkit-inner-spin-button,
        input[type=number]::-webkit-outer-spin-button {
          -webkit-appearance: none;
          margin: 0;
        }
        input[type=number] {
          -moz-appearance: textfield;
        }
        
        /* New Keypad Styles */
        .keypad-btn {
            @apply text-white rounded-lg text-3xl p-3 md:p-4 fredoka transition transform active:scale-95 active:translate-y-px shadow-lg active:shadow-md border-b-4;
            background-size: 100% 200%;
        }

        .btn-pink {
            @apply bg-pink-500 border-pink-700;
            background-image: linear-gradient(to top, #ec4899, #f472b6 50%, #f9a8d4 50%);
        }
        .btn-pink:hover {
             background-position: 0 100%;
        }

        .btn-green {
            @apply bg-emerald-500 border-emerald-700;
             background-image: linear-gradient(to top, #10b981, #34d399 50%, #6ee7b7 50%);
        }
        .btn-green:hover {
             background-position: 0 100%;
        }

        .btn-blue {
            @apply bg-blue-500 border-blue-700;
            background-image: linear-gradient(to top, #3b82f6, #60a5fa 50%, #93c5fd 50%);
        }
         .btn-blue:hover {
             background-position: 0 100%;
        }

        .btn-orange {
            @apply bg-orange-500 border-orange-700;
            background-image: linear-gradient(to top, #f97316, #fb923c 50%, #fdba74 50%);
        }
        .btn-orange:hover {
             background-position: 0 100%;
        }

        .btn-purple {
            @apply bg-purple-500 border-purple-700;
            background-image: linear-gradient(to top, #a855f7, #c084fc 50%, #d8b4fe 50%);
        }
        .btn-purple:hover {
             background-position: 0 100%;
        }

        .btn-yellow {
            @apply bg-yellow-400 border-yellow-600;
            background-image: linear-gradient(to top, #facc15, #fde047 50%, #fef08a 50%);
        }
        .btn-yellow:hover {
             background-position: 0 100%;
        }
    </style>
</head>
<body class="bg-blue-100 text-gray-800 flex items-center justify-center min-h-screen p-2 sm:p-4">

    <div class="w-full max-w-2xl mx-auto bg-white rounded-2xl shadow-2xl p-4 sm:p-6 md:p-10 space-y-6">

        <!-- Welcome Screen -->
        <div id="welcome-screen" class="screen active text-center">
            <h1 class="text-4xl md:text-6xl text-blue-600 fredoka mb-4">Math Adventure!</h1>
            <p class="text-lg mb-6">Let's practice some math and have fun!</p>
            <img src="https://placehold.co/400x200/a7f3d0/3b82f6?text=Welcome+Explorer!" class="mx-auto rounded-lg mb-6" alt="Welcome Image">
            <div class="space-y-4">
                <input type="text" id="name" placeholder="Enter your name" class="w-full p-3 border-2 border-blue-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                <input type="number" id="age" placeholder="Enter your age" class="w-full p-3 border-2 border-blue-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
             <button id="start-setup-btn" class="mt-6 w-full bg-green-500 hover:bg-green-600 text-white fredoka text-2xl py-3 rounded-lg transition-transform transform hover:scale-105">Let's Go!</button>
        </div>

        <!-- Setup Screen -->
        <div id="setup-screen" class="screen">
            <h2 class="text-3xl fredoka text-center text-blue-600 mb-6">Choose Your Challenge!</h2>
            <div class="grid grid-cols-2 gap-4 text-center">
                <div>
                    <input type="checkbox" id="addition" value="+" class="hidden peer">
                    <label for="addition" class="checkbox-label block p-6 bg-red-300 text-white rounded-lg cursor-pointer peer-checked:bg-red-500 peer-checked:ring-4 ring-red-200">
                        <span class="text-4xl">+</span><br>Addition
                    </label>
                </div>
                <div>
                    <input type="checkbox" id="subtraction" value="-" class="hidden peer">
                    <label for="subtraction" class="checkbox-label block p-6 bg-yellow-300 text-white rounded-lg cursor-pointer peer-checked:bg-yellow-500 peer-checked:ring-4 ring-yellow-200">
                        <span class="text-4xl">-</span><br>Subtraction
                    </label>
                </div>
                <div>
                    <input type="checkbox" id="multiplication" value="*" class="hidden peer">
                    <label for="multiplication" class="checkbox-label block p-6 bg-purple-300 text-white rounded-lg cursor-pointer peer-checked:bg-purple-500 peer-checked:ring-4 ring-purple-200">
                        <span class="text-4xl">×</span><br>Multiplication
                    </label>
                </div>
                <div>
                    <input type="checkbox" id="division" value="/" class="hidden peer">
                    <label for="division" class="checkbox-label block p-6 bg-indigo-300 text-white rounded-lg cursor-pointer peer-checked:bg-indigo-500 peer-checked:ring-4 ring-indigo-200">
                        <span class="text-4xl">÷</span><br>Division
                    </label>
                </div>
            </div>
            <button id="start-game-btn" class="mt-8 w-full bg-blue-500 hover:bg-blue-600 text-white fredoka text-2xl py-3 rounded-lg transition-transform transform hover:scale-105">Start Game</button>
        </div>

        <!-- Game Screen -->
        <div id="game-screen" class="screen">
            <div class="flex justify-between items-center mb-4 text-center">
                <div id="difficulty-display" class="fredoka text-lg md:text-xl text-blue-600 flex-1 text-left">Difficulty: Easy</div>
                <div id="question-timer-display" class="fredoka text-lg md:text-xl text-red-500 flex-1">Time: 0.0s</div>
                <div class="flex-1 text-right">
                    <button id="stop-game-btn" class="bg-red-500 hover:bg-red-600 text-white fredoka px-4 py-2 rounded-lg">Stop</button>
                </div>
            </div>
            <div id="question-container" class="text-center text-5xl md:text-7xl p-6 md:p-8 bg-gray-100 rounded-lg">
                <span id="num1"></span>
                <span id="operator"></span>
                <span id="num2"></span>
                <span> = ?</span>
            </div>
            <input type="number" id="answer" class="w-full text-4xl text-center mt-6 p-3 border-2 border-blue-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500" placeholder="Your Answer">
            
            <!-- Keypad for mobile -->
            <div id="keypad" class="grid grid-cols-5 gap-2 mt-4 w-full">
                <button class="keypad-btn btn-blue" data-key="1">1</button>
                <button class="keypad-btn btn-green" data-key="2">2</button>
                <button class="keypad-btn btn-pink" data-key="3">3</button>
                <button class="keypad-btn btn-orange" data-key="4">4</button>
                <button class="keypad-btn btn-purple" data-key="5">5</button>
                <button class="keypad-btn btn-green" data-key="6">6</button>
                <button class="keypad-btn btn-pink" data-key="7">7</button>
                <button class="keypad-btn btn-orange" data-key="8">8</button>
                <button class="keypad-btn btn-purple" data-key="9">9</button>
                <button class="keypad-btn btn-blue" data-key="0">0</button>
                <button class="keypad-btn btn-yellow col-span-5" data-key="clear">Clear</button>
            </div>
            
            <button id="submit-answer-btn" class="mt-4 w-full bg-green-500 hover:bg-green-600 text-white fredoka text-2xl py-3 rounded-lg transition-transform transform hover:scale-105">Submit</button>
            <div id="feedback" class="text-center text-2xl mt-4 font-bold h-8"></div>

        </div>

        <!-- Results Screen -->
        <div id="results-screen" class="screen text-center">
            <h2 class="text-4xl fredoka text-blue-600 mb-2">Great Session!</h2>
            <p class="text-lg mb-6" id="results-name-display"></p>
            <div class="bg-gray-100 p-6 rounded-lg space-y-4">
                <div class="flex justify-between items-center text-xl">
                    <span class="font-bold">Total Score:</span>
                    <span id="final-score" class="fredoka text-2xl text-green-600"></span>
                </div>
                <div class="flex flex-col items-center text-xl mt-4">
                    <span class="font-bold mb-2">Your Math IQ Score:</span>
                    <div id="iq-level-display" class="fredoka text-5xl text-purple-600"></div>
                </div>
            </div>
            <p class="text-sm font-semibold text-red-600 mt-4">Disclaimer: This is a fun, gamified score based on your performance in this game. It is not a real or clinically-valid IQ score.</p>
            <button id="play-again-btn" class="mt-6 w-full bg-blue-500 hover:bg-blue-600 text-white fredoka text-2xl py-3 rounded-lg transition-transform transform hover:scale-105">Play Again</button>
        </div>

    </div>

    <script>
        // DOM Elements
        const screens = {
            welcome: document.getElementById('welcome-screen'),
            setup: document.getElementById('setup-screen'),
            game: document.getElementById('game-screen'),
            results: document.getElementById('results-screen'),
        };
        const nameInput = document.getElementById('name');
        const ageInput = document.getElementById('age');
        const startSetupBtn = document.getElementById('start-setup-btn');
        const startGametBtn = document.getElementById('start-game-btn');
        const stopGameBtn = document.getElementById('stop-game-btn');
        const submitAnswerBtn = document.getElementById('submit-answer-btn');
        const playAgainBtn = document.getElementById('play-again-btn');
        const answerInput = document.getElementById('answer');
        const keypad = document.getElementById('keypad');
        
        // Game State
        let gameState = {};

        function resetGameState() {
            gameState = {
                name: '',
                age: 0,
                difficulty: 1,
                operators: [],
                score: 0,
                totalQuestionsAnswered: 0,
                totalCorrectAnswers: 0,
                totalAnswerTime: 0, // in seconds
                questionStartTime: 0,
                questionTimer: 0,
                questionTimerInterval: null,
            };
        }
        
        // --- Navigation ---
        function showScreen(screenName) {
            Object.values(screens).forEach(screen => screen.classList.remove('active'));
            screens[screenName].classList.add('active');
        }

        startSetupBtn.addEventListener('click', () => {
            const name = nameInput.value.trim();
            const age = parseInt(ageInput.value);

            if (!name || !age) {
                alert('Please enter your name and age.');
                return;
            }
            
            resetGameState();
            gameState.name = name;
            gameState.age = age;
            
            // Set initial difficulty based on age
            if (age <= 6) gameState.difficulty = 1;
            else if (age <= 8) gameState.difficulty = 3;
            else gameState.difficulty = 5;

            showScreen('setup');
        });

        startGametBtn.addEventListener('click', () => {
            const checkedBoxes = document.querySelectorAll('input[type="checkbox"]:checked');
            gameState.operators = Array.from(checkedBoxes).map(box => box.value);

            if (gameState.operators.length === 0) {
                alert('Please choose at least one type of math problem!');
                return;
            }
            startContinuousGame();
        });
        
        stopGameBtn.addEventListener('click', () => {
             clearInterval(gameState.questionTimerInterval);
             calculateAndShowFinalResults();
        });

        playAgainBtn.addEventListener('click', () => {
            // Go back to setup screen, keeping name and age
            showScreen('setup');
        });
        
        // --- Input Handling ---
        answerInput.addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                submitAnswerBtn.click();
            }
        });

        keypad.addEventListener('click', (event) => {
            const key = event.target.dataset.key;
            if (!key) return; // Exit if they clicked the gap between buttons

            if (key >= '0' && key <= '9') {
                answerInput.value += key;
            } else if (key === 'clear') {
                answerInput.value = '';
            }
            answerInput.focus();
        });

        // --- Game Logic ---
        function startContinuousGame() {
            // Reset stats for a new session
            gameState.score = 0;
            gameState.totalQuestionsAnswered = 0;
            gameState.totalCorrectAnswers = 0;
            gameState.totalAnswerTime = 0;
            
            showScreen('game');
            generateAndDisplayQuestion();
        }
        
        function updateDifficultyDisplay() {
            let difficultyText = 'Easy';
            if (gameState.difficulty > 15) difficultyText = 'Genius';
            else if (gameState.difficulty > 10) difficultyText = 'Expert';
            else if (gameState.difficulty > 7) difficultyText = 'Hard';
            else if (gameState.difficulty > 4) difficultyText = 'Medium';
            document.getElementById('difficulty-display').innerText = `Difficulty: ${difficultyText}`;
        }

        function generateAndDisplayQuestion() {
            const operator = gameState.operators[Math.floor(Math.random() * gameState.operators.length)];
            let num1, num2, answer;
            
            // Adjust number range based on difficulty
            const maxNum = gameState.difficulty * 5 + 5;
            const minNum = gameState.difficulty > 2 ? gameState.difficulty - 2 : 1;
            const multiplicationMax = Math.floor(gameState.difficulty / 2) + 2;

            if (operator === '+') {
                num1 = Math.floor(Math.random() * maxNum) + minNum;
                num2 = Math.floor(Math.random() * maxNum) + minNum;
                answer = num1 + num2;
            } else if (operator === '-') {
                const a = Math.floor(Math.random() * maxNum) + minNum;
                const b = Math.floor(Math.random() * maxNum) + minNum;
                num1 = Math.max(a, b);
                num2 = Math.min(a, b);
                answer = num1 - num2;
            } else if (operator === '*') {
                num1 = Math.floor(Math.random() * multiplicationMax) + 1;
                num2 = Math.floor(Math.random() * multiplicationMax) + 1;
                answer = num1 * num2;
            } else if (operator === '/') {
                const divisor = Math.floor(Math.random() * multiplicationMax) + 1;
                answer = Math.floor(Math.random() * multiplicationMax) + 1;
                num1 = divisor * answer;
                num2 = divisor;
            }
            
            gameState.currentQuestion = { num1, num2, operator, answer };
            
            document.getElementById('num1').innerText = num1;
            document.getElementById('operator').innerText = operator === '*' ? '×' : operator;
            document.getElementById('num2').innerText = num2;
            answerInput.value = '';
            answerInput.focus();
            
            updateDifficultyDisplay();
            
            // Start the visual timer for this question
            gameState.questionStartTime = Date.now();
            gameState.questionTimer = 0;
            const timerDisplay = document.getElementById('question-timer-display');
            timerDisplay.innerText = `Time: 0.0s`;

            clearInterval(gameState.questionTimerInterval); // Clear previous timer
            gameState.questionTimerInterval = setInterval(() => {
                gameState.questionTimer += 0.1;
                timerDisplay.innerText = `Time: ${gameState.questionTimer.toFixed(1)}s`;
            }, 100);
        }

        submitAnswerBtn.addEventListener('click', () => {
            if (answerInput.value === '') return;
            
            clearInterval(gameState.questionTimerInterval); // Stop the question timer

            const answerTime = (Date.now() - gameState.questionStartTime) / 1000; // in seconds
            gameState.totalAnswerTime += answerTime;
            gameState.totalQuestionsAnswered++;

            const userAnswer = parseInt(answerInput.value);
            const correctAnswer = gameState.currentQuestion.answer;
            const feedbackEl = document.getElementById('feedback');
            const questionContainer = document.getElementById('question-container');

            feedbackEl.classList.remove('correct-animation', 'incorrect-animation', 'text-green-500', 'text-red-500');
            questionContainer.classList.remove('correct-animation', 'incorrect-animation');
            
            if (userAnswer === correctAnswer) {
                gameState.totalCorrectAnswers++;
                gameState.score += gameState.difficulty * 10;
                feedbackEl.innerText = "Correct!";
                feedbackEl.classList.add('text-green-500');
                questionContainer.classList.add('correct-animation');
                
                // Adaptive difficulty: increase if correct and fast
                if (answerTime < 3) {
                    gameState.difficulty++;
                } else if (answerTime < 5) {
                    // Stays the same
                } else {
                    // Decrease difficulty if correct but slow
                    if (gameState.difficulty > 1) gameState.difficulty--;
                }

            } else {
                feedbackEl.innerText = `Oops! The answer was ${correctAnswer}`;
                feedbackEl.classList.add('text-red-500');
                questionContainer.classList.add('incorrect-animation');
                
                // Adaptive difficulty: decrease if incorrect
                if (gameState.difficulty > 1) gameState.difficulty--;
            }
            
            // Move to next question after a short delay
            setTimeout(() => {
                feedbackEl.innerText = "";
                questionContainer.classList.remove('correct-animation', 'incorrect-animation');
                generateAndDisplayQuestion();
            }, 1200);
        });
        
        function calculateAndShowFinalResults() {
            clearInterval(gameState.questionTimerInterval);
            if(gameState.totalQuestionsAnswered === 0) {
                 // Handle case where user stops without answering
                 showScreen('setup');
                 return;
            }
            
            // --- IQ Score Calculation ---
            // A fun, gamified calculation, not a real IQ test.
            // Base average IQ is 100.
            let iq = 100;

            // 1. Adjust for Difficulty Reached
            // For every point of difficulty reached above a baseline of 5, add IQ points.
            iq += (gameState.difficulty - 5) * 4;

            // 2. Adjust for Accuracy
            const accuracy = (gameState.totalCorrectAnswers / gameState.totalQuestionsAnswered) * 100;
            // Reward high accuracy, penalize low accuracy.
            iq += (accuracy - 85) * 1.2; 

            // 3. Adjust for Speed
            const averageTime = gameState.totalAnswerTime / gameState.totalQuestionsAnswered;
            // Reward speed faster than 5 seconds, penalize for being slower.
            // This has a significant impact.
            iq += (5 - averageTime) * 5;
            
            // Normalize score to be within a reasonable range (e.g., 70-170)
            iq = Math.max(70, Math.min(170, Math.round(iq)));

            document.getElementById('results-name-display').innerText = `Well done, ${gameState.name}!`;
            document.getElementById('final-score').innerText = gameState.score;
            document.getElementById('iq-level-display').innerText = iq;
            
            showScreen('results');
        }

        // Initial setup
        resetGameState();
        showScreen('welcome');
    </script>
</body>
</html>
